package com.score.callmetest.util

import android.annotation.SuppressLint
import java.util.concurrent.TimeUnit

object TimeUtils {


    /**
     * 工具方法：将秒数转换为 hh:mm:ss 格式的时间字符串
     *
     * @param seconds 输入的秒数
     * @return 格式化后的时间字符串，格式为 hh:mm:ss
     */
    @SuppressLint("DefaultLocale")
    fun formatSecondsToTime(seconds: Long): String {
        val h = seconds / 3600
        val m = (seconds % 3600) / 60
        val s = seconds % 60
        return String.format("%02d:%02d:%02d", h, m, s)
    }

    /**
     * 将毫秒数格式化为 hh:mm:ss 格式的时间字符串
     *
     * @param milliseconds 输入的毫秒数
     * @return 格式化后的时间字符串，格式为 hh:mm:ss
     */
    @SuppressLint("DefaultLocale")
    fun formatTime(milliseconds: Long): String {
        val hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    /**
     * 将秒数格式化为可读的时间格式（如：1h2m3s）
     *
     * @param seconds 输入的秒数
     * @return 格式化后的时间字符串，格式为 XhYmZs（根据时长自动省略不需要的部分）
     */
    fun formatDurationToReadable(seconds: Int): String {
        if (seconds < 0) return "0s"

        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val remainingSeconds = seconds % 60

        val result = StringBuilder()

        if (hours > 0) {
            result.append("${hours}h")
        }

        if (minutes > 0) {
            result.append("${minutes}m")
        }

        result.append("${remainingSeconds}s")

        return result.toString()
    }
}