package com.score.callmetest.ui.web

import android.annotation.SuppressLint
import android.os.Build
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.addCallback
import com.score.callmetest.databinding.ActivityWebViewBinding
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import timber.log.Timber

class WebViewActivity : BaseActivity<ActivityWebViewBinding, EmptyViewModel>() {

    private val NAME = "JSBridgeService"

    override fun getViewBinding(): ActivityWebViewBinding {
        return ActivityWebViewBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    override fun initView() {
        onBackPressedDispatcher.addCallback(this){
            if (binding.webView.canGoBack()) {
                binding.webView.goBack()
            } else {
                finish()
            }
        }

        setupWebView()
        setupToolbar()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        val url = intent.getStringExtra("url") ?: "https://example.com"
        val title = intent.getStringExtra("title") ?: "Web Page"

        binding.toolbar.title = title

        binding.webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
        }

        binding.webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                url?.let { view?.loadUrl(it) }
                return true
            }
        }

        binding.webView.loadUrl(url)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)

        binding.toolbar.setNavigationOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
    }

    // <editor-folder desc="JSBridgeService">

    //关闭当前webView
    @JavascriptInterface
    fun newTppClose() {
        //需要实现  关闭当前应用内WebView
    }
    
    //webview事件回调
    @JavascriptInterface
    fun newTppLogEvent(json: String?) {

    }
    
    //打开官方客服私聊页
    @JavascriptInterface
    fun openVipService() {

    }
    
   //提供支持api
    @JavascriptInterface
    fun getSupportApi(): String {
        List<String> api = new ArrayList<>();
        api.add("newTppClose");
        api.add("newTppLogEvent");
        api.add("openVipService");
        api.add("recharge");
        api.add("rechargeSource");
        return GsonUtil.getInstance().getGson().toJson(api);
    }
    
    //调起充值页面
    @JavascriptInterface
    fun recharge() {

    }
    
    //获取充值来源
    @JavascriptInterface
    fun rechargeSource(json: String?) {

    }

    //</editor-folder>

}