package com.score.callmetest.ui.videocall

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCallOutgoingBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.manager.CallSource
import com.score.callmetest.manager.CallType
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.HangUpReason
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.RingtoneManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VideoCallManager
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.network.LiveCallExt2
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.widget.Helper.PhotoPagerHelper
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import timber.log.Timber

/**
 * 拨打视频
 */
class CallOutgoingFragment(
    val userId: String,
    val avatarUrl: String,
    val nickname: String,
    val age: String,
    val country: String,
    val unitPrice: String,
) : BaseFragment<FragmentCallOutgoingBinding, CallOutgoingViewModel>() {
    fun getVideoActivity(): VideoCallActivity? {
        return activity as? VideoCallActivity
    }

    private var callHandler: Handler? = null
    private var call10sRunnable: Runnable? = null
    private var call30sRunnable: Runnable? = null

    override fun getViewModelClass() = CallOutgoingViewModel::class.java
    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?) =
        FragmentCallOutgoingBinding.inflate(inflater, container, false)

    private var channelName: String? = null

    override fun initView() {
        StrategyManager.addReviewedUsers(userId)
        if(StrategyManager.isReviewPkg()){
            // 所有接收到的地方，都需要把此人的status设置为GlobalManager.getReviewOtherStatus()
            EventBus.post(CustomEvents.ReviewCallEvent(userId))
        }

        binding.ageLayout.doOnPreDraw {
            binding.ageLayout.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.TRANSPARENT,
                radiusDp = binding.ageLayout.height / 2f,
                strokeColor = resources.getColor(R.color.age_color),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }

        binding.tvCountry.doOnPreDraw {
            binding.tvCountry.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.TRANSPARENT,
                radiusDp = binding.tvCountry.height / 2f,
                strokeColor = resources.getColor(R.color.country_color),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }

        showDefaultData()

        // 1. 先尝试从缓存获取并展示
        // 2. 异步获取最新数据，获取到后更新缓存和界面
        UserInfoManager.getUserInfo(userId) { getUserInfo ->
            getUserInfo?.let { updateUserInfoUI(it) }
        }

        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00080029".toColorInt(), Color.BLACK),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )

        // 呼叫界面进入
        LogReportManager.reportLiveCallEvent(
            action = LiveCallAction.ENTER,
            ext = LiveCallExt.CALLING
        )
    }

    fun showDefaultData() {
        GlideUtils.load(requireContext(), avatarUrl, binding.ivAvatar)

        binding.tvNickname.text = nickname
        binding.tvNickname.requestFocus()
        binding.tvAge.text = age
        binding.tvCountry.text = country
        // 费用说明：审核模式隐藏价格，否则高亮价格
        val isReview = StrategyManager.strategyConfig?.isReviewPkg
        if (!isReview!!) {
            val price = if (unitPrice.isEmpty()) "Free" else unitPrice
            val fullText = "You will be charged $price coins per minute."
            val spannable = SpannableString(fullText)
            val start = fullText.indexOf(price)
            val end = start + price.length
            spannable.setSpan(
                ForegroundColorSpan("#2DE314".toColorInt()),
                start, end,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.tvPrice.text = spannable
        } else {
            binding.tvPrice.text = "You will be charged 0 coins per minute."
        }

        if (StrategyManager.isReviewPkg()) {
            binding.tvPrice.text = ""
        }
    }

    override fun initData() {
        startRingtone()
        startCallTimeouts()
        viewModel.createChannel(
            toUserId = userId,
            callType = CallType.PRIVATE,
            callSource = CallSource.POPULAR_WALL,
            onSuccess = { channelData ->
                if (channelData != null) {
                    // 加入频道
                    VideoCallManager.joinChannelWithResponseData(channelData)
                    channelName = channelData.channelName

                    AgodaUtils.observeJoinChannelSuccess(this) { event ->
                        // 收到加入频道成功消息，则监听挂断或接听事件
                        // 等待长链接收 接听 事件（使用双通道去重）
                        DualChannelEventManager.observeOnHangUp(this) { event ->
                            // 对方挂断，保存为UNANSWERED_CALL
                            getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
                                videoCallViewModel.saveCallHistory(com.score.callmetest.CallType.UNANSWERED_CALL)
                            }
                            viewModel.hangup(
                                channelName = event.channelName,
                                hangUpReason = HangUpReason.CL_REMOTE_USER_LEFT
                            )
                            requireActivity().finish()
                        }
                    }

                    LogReportManager.reportLiveCallEvent(
                        channelName = channelName,
                        action = LiveCallAction.CALL_RESP,
                        ext = LiveCallExt.OK
                    )
                }
            },
            onError = { errorMsg ->
                LogReportManager.reportLiveCallEvent(
                    channelName = channelName,
                    action = LiveCallAction.CALL_RESP,
                    ext = LiveCallExt.ERROR,
                    ext2 = errorMsg
                )
                Timber.e(errorMsg)
//                ToastUtils.showToast(errorMsg)
                ToastUtils.showToast(CallmeApplication.context.getString(R.string.net_error_and_try_again))
                requireActivity().finish()
            }
        )
    }

    override fun initListener() {
        binding.btnCancel.click {
            LogReportManager.reportLiveCallEvent(
                channelName = channelName,
                action = LiveCallAction.HANGUP,
                ext = LiveCallExt.CALLING,
                ext2 = LiveCallExt2.HANGUP_BUTTON
            )

            // 用户主动取消通话，保存为CANCELLED_CALL
            getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
                videoCallViewModel.saveCallHistory(com.score.callmetest.CallType.CANCELLED_CALL)
            }
            viewModel.hangup(viewModel.channelName)
            requireActivity().finish()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateUserInfoUI(userInfo: com.score.callmetest.network.UserInfo) {
        // 组装图片列表（优先mediaList和头像）
        val realImageUrls = mutableListOf<String>()
        if (!userInfo.avatarThumbUrl.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatarThumbUrl)
        } else if (!userInfo.avatarUrl.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatarUrl)
        } else if (!userInfo.avatar.isNullOrEmpty()) {
            realImageUrls.add(userInfo.avatar)
        }
        if (!userInfo.mediaList.isNullOrEmpty()) {
            realImageUrls.addAll(userInfo.mediaList.mapNotNull { it.mediaUrl })
        }
        PhotoPagerHelper.setupPhotoPager(
            viewPager = binding.photoPager,
            imageUrls = realImageUrls,
            context = requireContext(),
            onImageClick = { position, urls ->
                // 拦截图片点击，不进入预览大图
            }
        )

        // 其余UI
        binding.tvNickname.text = userInfo.nickname ?: ""
        binding.tvNickname.requestFocus()
        binding.tvAge.text = userInfo.age?.toString() ?: ""
        binding.tvCountry.text = userInfo.country ?: ""
        // 费用说明：审核模式隐藏价格，否则高亮价格
        val isReview = StrategyManager.strategyConfig?.isReviewPkg
        if (!isReview!! && userInfo.unitPrice != null) {
            val price = "${userInfo.unitPrice}"
            val fullText = "You will be charged $price coins per minute."
            val spannable = SpannableString(fullText)
            val start = fullText.indexOf(price)
            val end = start + price.length
            spannable.setSpan(
                ForegroundColorSpan("#2DE314".toColorInt()),
                start, end,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.tvPrice.text = spannable
        } else {
            binding.tvPrice.text = "You will be charged 0 coins per minute."
        }

        if (StrategyManager.isReviewPkg()) {
            binding.tvPrice.text = ""
        }
    }

    private fun startRingtone() {
//        RingtoneManager.playRingtone(requireContext())
    }

    private fun stopRingtone() {
//        RingtoneManager.stopRingtone()
    }

    private fun startCallTimeouts() {
        callHandler = Handler(Looper.getMainLooper())
        call10sRunnable = Runnable {
//            ToastUtils.showToast("The user's phone may not be around, please try again later")
            ToastUtils.showToast(CallmeApplication.context.getString(R.string.user_not_around))
        }
        call30sRunnable = Runnable {
//            ToastUtils.showToast("The user is not available now, please try again later")
            ToastUtils.showToast(CallmeApplication.context.getString(R.string.user_not_available_try_again))

            LogReportManager.reportLiveCallEvent(
                channelName = channelName,
                action = LiveCallAction.HANGUP,
                ext = LiveCallExt.CALLING,
                ext2 = LiveCallExt2.TIME_OUT
            )
            
            // 超时挂断，保存为UNANSWERED_CALL
            getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
                videoCallViewModel.saveCallHistory(com.score.callmetest.CallType.UNANSWERED_CALL)
            }
            viewModel.hangup(
                channelName = viewModel.channelName,
                hangUpReason = HangUpReason.CL_CALL_TIMEOUT
            )
            requireActivity().finish()
        }
        callHandler?.postDelayed(call10sRunnable!!, 10_000)
        callHandler?.postDelayed(call30sRunnable!!, 30_000)
    }

    private fun clearCallTimeouts() {
        call10sRunnable?.let { callHandler?.removeCallbacks(it) }
        call30sRunnable?.let { callHandler?.removeCallbacks(it) }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        PhotoPagerHelper.cleanup()
        stopRingtone()
        clearCallTimeouts()

        // 呼叫界面进入
        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.EXIT,
            ext = LiveCallExt.CALLING
        )
    }


} 