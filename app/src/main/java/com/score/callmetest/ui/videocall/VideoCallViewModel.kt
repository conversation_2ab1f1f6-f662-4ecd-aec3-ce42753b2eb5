package com.score.callmetest.ui.videocall

import android.util.Log
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallmeApplication
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.launch
import timber.log.Timber


class VideoCallViewModel : BaseViewModel() {
    var userId: String? = null

    // 通话相关信息
    var avatarUrl: String? = null
    var nickname: String? = null
    var callStartTime: Long = 0L
    var callEndTime: Long = 0L
    var hasEnteredOngoing: Boolean = false // 是否进入了通话中状态

    /**
     * 保存通话记录到数据库
     * @param callType 通话类型，如 CallType.REJECTED_CALL
     * @param callDuration 通话时长（秒），默认为0
     */
    fun saveCallHistory(callType: String, callDuration: Long = 0L) {
        if (userId.isNullOrEmpty()) {
            Timber.tag("VideoCallViewModel").w("saveCallHistory: userId is empty, skip saving")
            return
        }

        viewModelScope.launch {
            try {
                // 通过UserInfoManager获取用户信息
                UserInfoManager.getUserInfo(userId!!) { userInfo ->
                    if (userInfo != null) {
                        // 使用获取到的用户信息构建通话记录
                        val callHistory = CallHistoryEntity(
                            id = "", // 新建记录时id为空，数据库会自动生成UUID
                            userId = userId!!,
                            currentUserId =  UserInfoManager.myUserInfo?.userId ?: "",
                            userName = userInfo.nickname ?: nickname ?: "",
                            avatar = userInfo.avatarUrl ?: userInfo.avatarThumbUrl ?: userInfo.avatarMiddleThumbUrl ?: avatarUrl ?: "",
                            unitPrice = userInfo.unitPrice ?: -1,   //如果获取不到单位价格，则设置为-1
                            callType = callType,
                            callDuration = callDuration,
                            callEndTime = System.currentTimeMillis(),
                            hasVideo = true, // 视频通话
                            onlineStatus = com.score.callmetest.CallStatus.OFFLINE, // 默认离线状态
                            isPinned = false,
                            isBottomView = false
                        )

                        DatabaseFactory.getDatabase(CallmeApplication.context)
                            .insertCallHistory(callHistory) { success ->
                                if (success) {
                                    Timber.tag("VideoCallViewModel").d("Call history saved successfully: ${callHistory.callType} for user ${callHistory.userId}, avatar=${callHistory.avatar}")
                                } else {
                                    Timber.tag("VideoCallViewModel").e("Failed to save call history: ${callHistory.callType} for user ${callHistory.userId}")
                                }
                            }
                    } else {
                        Timber.tag("VideoCallViewModel").w("saveCallHistory: failed to get user info for userId: $userId")
                    }
                }
            } catch (e: Exception) {
                Timber.tag("VideoCallViewModel").e(e, "Error saving call history")
            }
        }
    }



    /**
     * 标记通话开始时间
     */
    fun markCallStart() {
        callStartTime = System.currentTimeMillis()
        Timber.d("Call started at: $callStartTime")
    }

    /**
     * 标记通话结束时间并计算通话时长
     * @return 通话时长（秒）
     */
    fun markCallEnd(): Long {
        callEndTime = System.currentTimeMillis()
        val duration = if (callStartTime > 0) {
            (callEndTime - callStartTime) / 1000
        } else {
            0L
        }
        Timber.d("Call ended at: $callEndTime, duration: ${duration}s")
        return duration
    }

    /**
     * 设置用户信息
     */
    fun setUserInfo(userId: String, nickname: String?, avatarUrl: String?) {
        this.userId = userId
        this.nickname = nickname
        this.avatarUrl = avatarUrl
        Timber.d("User info set: userId=$userId, nickname=$nickname. avatarUrl=$avatarUrl")
    }
}