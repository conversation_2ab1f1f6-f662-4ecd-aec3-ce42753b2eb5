package com.score.callmetest.ui.base

import android.os.Build
import android.provider.Settings
import android.util.Base64
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.LoginData
import com.score.callmetest.network.LoginRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.RiskInfo
import com.score.callmetest.network.StrategyConfig
import com.score.callmetest.util.AESUtils
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.GPSUtils
import com.score.callmetest.util.InputMethodUtils
import com.score.callmetest.util.LocaleUtils
import com.score.callmetest.util.NetworkUtils
import com.score.callmetest.util.ProxyUtils
import com.score.callmetest.util.SIMUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeZoneUtils
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import retrofit2.HttpException
import timber.log.Timber
import java.io.IOException

open class BaseViewModel : ViewModel() {
    // BaseViewModel现在只作为基础类，具体业务逻辑移到对应的ViewModel中

    fun doLogin(
        oauthType: Int,
        token: String,
        onSuccess: (LoginData) -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                Log.d("LoginViewModel", "before execute login request")
                val relogin = SharePreferenceUtil.getInt(Constant.RELOGIN, 0)
                val info = getRiskInfo()
                val loginRequest = LoginRequest(
                    oauthType = oauthType,
                    token = token,
                    relogin = relogin,
                    info = info
                )
                val response = RetrofitUtils.dataRepository.login(loginRequest)
                if (response is NetworkResult.Success) {
                    response.data?.let { onSuccess(it) }
                } else {
                    onError("Login failed: $response")
                }
            } catch (e: IOException) {
                Log.e("LoginViewModel", "Network error", e)
                onError("Network error: ${e.localizedMessage}")
            } catch (e: HttpException) {
                Log.e("LoginViewModel", "HTTP error", e)
                onError("HTTP error: ${e.localizedMessage}")
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Unexpected error", e)
                onError("Unexpected error: ${e.localizedMessage}")
            }
        }
    }

    fun getRiskInfo(): String? {
        val kFactor = AppConfigManager.getRiskControlConfig()?.k_factor ?: ""
        val systemLanguage = LocaleUtils.getSystemLanguageCountry()
        val userId = UserInfoManager.myUserInfo?.userId
        val riskInfo = RiskInfo(
            platform = Constant.HEADER_PLATFORM_VALUE,
            pkg = CallmeApplication.context.packageName,
            ver = BuildConfig.VERSION_NAME,
            platform_ver = Build.VERSION.SDK_INT.toString(),
            model = Build.MODEL,
            user_id = userId,
            device_id = DeviceUtils.getAndroidId(),
            is_enable_vpn = if (BuildConfig.DEBUG) "0" else {
                if (NetworkUtils.isVPNEnabled()) "1" else "0"
            },
            is_enable_proxy = if (BuildConfig.DEBUG) "0" else {
                if (ProxyUtils.isProxyEnabled()) "1" else "0"
            },
            system_language = systemLanguage,
            sim_country = SIMUtils.getSIMCountry(),
            time_zone = TimeZoneUtils.getCurrentTimeZoneId() ?: "",
            is_auto_time_zone =
                Settings.Global.getInt(
                    CallmeApplication.context.contentResolver,
                    Settings.Global.AUTO_TIME_ZONE, 0
                ).toString(),
            gps_longitude = GPSUtils.getLongitude(),
            gps_latitude = GPSUtils.getLatitude(),
            input_language = InputMethodUtils.getInputLanguages(),
            is_emulator = if (BuildConfig.DEBUG) "0" else {
                if (DeviceUtils.isEmulator()) "1" else "0"
            },
            is_run_in_virtual = if (BuildConfig.DEBUG) "0" else {
                if (DeviceUtils.isRunInVirtual()) "1" else "0"
            },
            is_rooted = if (BuildConfig.DEBUG) "0" else {
                if (DeviceUtils.isRooted()) "1" else "0"
            },
            is_hook = if (BuildConfig.DEBUG) "0" else {
                // todo 下面需求权限
//                if (DeviceUtils.isHooked(CallmeApplication.context)) "1" else "0"
                "0"
            }
        )
        Timber.tag("getRiskInfo").d(riskInfo.toString())
        return encryptRiskInfo(riskInfo, kFactor)
    }

    fun encryptRiskInfo(riskInfo: RiskInfo, kFactor: String?): String? {
        if (kFactor.isNullOrEmpty()) return null
        return try {
            val json = Json.encodeToString(riskInfo)
            Timber.tag("getRiskInfo").d("json:" + json)
            val aesEncrypted = AESUtils.encrypt128(
                content = json,
                key = kFactor
            ) // 128位 AES/ECB/PKCS5Padding

            Timber.tag("getRiskInfo").d("before:" + aesEncrypted)
            val out = AESUtils.decrypt128(aesEncrypted, kFactor)
            Timber.tag("getRiskInfo").d("after:" + out)
            aesEncrypted
        } catch (e: Exception) {
            null
        }
    }

    fun getStrategy(
        onSuccess: (StrategyConfig) -> Unit,
        onError: (String) -> Unit
    ) {
        StrategyManager.getStrategy(
            forceRefresh = true,
            onSuccess = {
                GlobalManager.onLoginSuccess()

                UserInfoManager.refreshMyUserInfo()

                // 初始化Agora
                AgodaUtils.initializeGlobal(CallmeApplication.context)

                ThreadUtils.runOnMain {

                    // 融云--需要主线程，不然会出现Cannot invoke observeForever on a background thread错误
                    val rcKey = AppConfigManager.getConfigValue("rc_app_key")
                    val rcAreaCode = AppConfigManager.getConfigValue("rc_area_code")
                    RongCloudManager.init(CallmeApplication.getInstance(), rcKey, rcAreaCode)

                    onSuccess(it)
                }
            }, onError = {
                ThreadUtils.runOnMain { onError(it) }
            }
        )
    }
} 