package com.score.callmetest.ui.mine.blockList

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentFollowContentBinding
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.util.ToastUtils

class BlockListFragment : BaseFragment<FragmentFollowContentBinding, BlockListViewModel>() {

    private lateinit var adapter: BlockListAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }
    private var mNeedScrollToTop = false

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentFollowContentBinding {
        return FragmentFollowContentBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = BlockListViewModel::class.java

    override fun initView() {
        super.initView()
        setupRecyclerView()
        setupSwipeRefresh()
    }

    private fun setupRecyclerView() {
        adapter = BlockListAdapter { item ->
            item.broadcasterId?.let {
                viewModel.unblock(it) { success ->
                    if (success) {
                        ToastUtils.showShortToast(getString(com.score.callmetest.R.string.unblock_successfully))
                        loadBlockList(isRefresh = true)
                    } else {
                        ToastUtils.showShortToast(getString(com.score.callmetest.R.string.unblock_failed))
                    }
                }
            }
        }

        binding.recyclerView.apply {
            adapter = <EMAIL>
            layoutManager = mLayoutManager

            // 添加滚动监听，实现加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有向下滚动时才检查
                    if (dy > 0) {
                        val visibleItemCount = mLayoutManager.childCount
                        val totalItemCount = mLayoutManager.itemCount
                        val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()

                        // 当滚动到倒数第5个item时开始加载更多
                        if (!viewModel.isLoadingMore() &&
                            viewModel.hasMoreData() &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5) {
                            loadMoreBlockList()
                        }
                    }
                }
            })
        }
    }

    /**
     * 更新底部项显示状态
     */
    private fun updateBottomItemVisibility() {
        binding.recyclerView.post {
            val shouldShowBottom = adapter.itemCount > 0
            adapter.setShowBottom(shouldShowBottom)
        }
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
            setOnRefreshListener {
                isRefreshing = true
                mNeedScrollToTop = true
                // 重新加载 block 列表
                loadBlockList(isRefresh = true)
            }
        }
    }

    private fun loadBlockList(isRefresh: Boolean = true) {
        // 调用ViewModel加载屏蔽列表
        viewModel.fetchBlockList(isRefresh)
    }

    private fun loadMoreBlockList() {
        // 加载更多屏蔽数据
        viewModel.fetchBlockList(isRefresh = false)
    }

    override fun initData() {
        super.initData()
        setupObservers()
        binding.emptyView.inflate()
        // 初始加载数据
        loadBlockList(isRefresh = true)
    }

    private fun setupObservers() {
        // 监听 blockList 数据
        viewModel.blockList.observe(viewLifecycleOwner) { list ->
            // 显示/隐藏空视图
            emptyView(list.isNullOrEmpty())

            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

            // 更新适配器数据
            adapter.setData(list ?: emptyList())

            // 数据加载完成后，停止刷新动画
            binding.swipeRefreshLayout.isRefreshing = false

            // 只有在需要滚动到顶部时才滚动（下拉刷新时）
            if (mNeedScrollToTop) {
                binding.recyclerView.scrollToPosition(0)
                mNeedScrollToTop = false
            } else if (firstVisiblePosition == 0) {
                // 如果原本就在顶部，确保仍然在顶部
                binding.recyclerView.scrollToPosition(0)
            }

            // 列表更新后检查底部项显示状态
            updateBottomItemVisibility()
        }

        // 观察加载状态 - 只有在刷新时才显示顶部加载动画
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // 只有在不是加载更多的情况下才显示顶部加载动画
            if (!viewModel.isLoadingMore()) {
                binding.swipeRefreshLayout.isRefreshing = isLoading
                if (isLoading) {
                    mNeedScrollToTop = true
                }
            }
        }

        // 监听错误信息
        viewModel.error.observe(viewLifecycleOwner) { msg ->
            if (!msg.isNullOrEmpty()) {
                ToastUtils.showShortToast(msg)
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.blockList.value.isNullOrEmpty())
        }
    }

    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        if (isEmpty) {
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

}