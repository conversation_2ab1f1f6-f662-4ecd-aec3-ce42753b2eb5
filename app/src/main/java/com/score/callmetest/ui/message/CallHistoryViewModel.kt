package com.score.callmetest.ui.message

import android.os.Looper
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.room.concurrent.AtomicBoolean
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.im.callback.ImOperationCallback
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.collections.set


/**
 * 通话记录列表ViewModel
 */
class CallHistoryViewModel: BaseViewModel() {


    // 会话消息列表数据
    private val _goChatUser = MutableLiveData<UserInfo>()
    val mGoChatUser: LiveData<UserInfo> = _goChatUser
    // 通话记录列表数据
    private val _callHistoryList = MutableLiveData<List<CallHistoryEntity>>()
    val callHistoryList: LiveData<List<CallHistoryEntity>> = _callHistoryList

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 是否显示底部项的标志
    private var mShouldShowBottomItem = AtomicBoolean(false)

    init {
        // 初始化时加载通话记录列表
        loadCallHistoryList()
    }

    /**
     * 加载通话记录列表
     */
    fun loadCallHistoryList() {
        _isLoading.value = true
        viewModelScope.launch {
            try {
                // 数据库读取
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .getCurrentUserAllCallHistories()
                    .catch { e ->
                        Timber.e("getAllHistoryLists---$e")
                        _errorMessage.value = "加载消息列表失败: ${e.message}"
                        _isLoading.value = false
                    }.collect { list ->
                        Timber.d("collect....${Thread.currentThread()}")
                        if(list.isNotEmpty()) {
                            updateCallHistoryPre { currentList ->
                                // 重新刷新--忽略当前list
                                list
                            }
                        }

                        _isLoading.value = false
                    }
            } catch (e: Exception) {
                _errorMessage.value = "加载通话历史记录失败: ${e.message}"
                _isLoading.value = false
            }
        }
    }


    /**
     * 更新通话历史记录的通用方法
     *
     * @param isSetValue 是否使用 setValue
     * @param update 更新操作
     */
    private fun updateCallHistoryPre(isSetValue: Boolean = true,update: (List<CallHistoryEntity>) -> List<CallHistoryEntity>) {
        viewModelScope.launch {
            val currentList = _callHistoryList.value ?: emptyList()
            val updatedList = update(currentList)

            // 排序消息列表并重新添加底部项
            updateList(sortCallHistory(updatedList),isSetValue)
        }
    }
    /**
     * 更新列表数据，根据条件决定是否添加底部项(一般不会直接调用这个，而是 [updateCallHistoryPre] )
     *
     * @param callHistory
     * @param isSetValue 是否使用LiveData.postValue()执行--不在主线程只能postValue
     *
     */
    private fun updateList(callHistory: List<CallHistoryEntity>, isSetValue: Boolean = true) {
        
        val list = if (mShouldShowBottomItem.get()) {
            // 需要bottom
            if (callHistory.isNotEmpty() && !callHistory.last().isBottomView) {
                callHistory + CallHistoryEntity.provideBottomView()
            } else {
                callHistory
            }
        } else {
            // 不需要bottom
            mShouldShowBottomItem.set(false)
            callHistory.filter { !it.isBottomView }
        }

        if(isSetValue && (Looper.getMainLooper() === Looper.myLooper())) {
            _callHistoryList.value = list
        }else {
            _callHistoryList.postValue(list)
        }
    }
    /**
     * 对通话历史列表进行排序
     * 置顶的消息排在前面，然后按时间戳排序
     * @param list 原始消息列表
     * @return 排序后的消息列表
     */
    private fun sortCallHistory(list: List<CallHistoryEntity>): List<CallHistoryEntity> {
        // 使用Kotlin的分组和排序功能
        return list.sortedWith(
            compareByDescending<CallHistoryEntity> {
                it.isPinned
            }
                .thenByDescending { it.callEndTime }
        ).map { entity ->
            // 适配审核模式
            if (StrategyManager.isReviewPkg()) {
                getUserInfo(entity.userId) { userInfo ->
                    if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                        entity.onlineStatus = CallStatus.ONLINE
                    }else {
                        entity.onlineStatus = GlobalManager.getReviewOtherStatus(userInfo?.userId)
                    }
                }
            }

            entity
        }
    }


    /**
     * 更新状态 - 通话历史记录不需要更新状态，状态由Adapter实时获取
     * @param [statusMap] 状态map
     */
    fun updateStatus(statusMap: Map<String, String>) {
        updateCallHistoryPre { currentList ->
            val list = currentList.map { map ->
                if(statusMap.containsKey(map.userId)){

                    // 存在--以map的状态为主
                    val newMap = map.copy(
                        onlineStatus = statusMap[map.userId] ?: CallStatus.OFFLINE
                    )
                    // 更新数据库
                    DatabaseFactory.getDatabase(CallmeApplication.context)
                        .updateCurrentUserCallHistory(newMap)

                    return@map newMap
                }else  return@map map
            }
            return@updateCallHistoryPre list
        }
    }

    // <editor-fold desc="popupView相关操作">
    
    /**
     * 隐藏通话记录
     * @param callHistoryObj 要隐藏的通话记录
     */
    fun hideMessage(callHistoryObj: CallHistoryEntity) {
        updateCallHistoryPre { currentList ->
            // 本地隐藏（从列表中移除，但不删除数据库记录）
            currentList.filter { it.userId != callHistoryObj.userId }
        }
    }
    /**
     *
     * 点击item查询userinfo后跳转
     */
    fun gotoChat(userId: String){
        // 先查缓存，再网络查询
        UserInfoManager.getUserInfo(userId){ getUserInfo ->
            getUserInfo?.let { nonNullUser ->
                _goChatUser.value = nonNullUser
            }
        }
    }
    /**
     * 删除一条通话记录
     * @param callHistoryObj 要删除的消息
     */
    fun deleteMessage(callHistoryObj: CallHistoryEntity) {
        updateCallHistoryPre { currentList ->
            // 修复：根据id删除具体的通话记录，而不是删除该用户的所有记录
            if (callHistoryObj.id.isNotEmpty()) {
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .deleteCallHistoryById(callHistoryObj.id) { success ->
                        if (success) {
                            Timber.d("Call history deleted successfully: ${callHistoryObj.id}")
                        } else {
                            Timber.e("Failed to delete call history: ${callHistoryObj.id}")
                        }
                    }
            } else {
                Timber.w("Cannot delete call history: id is empty")
            }

            // 融云删除
            if (callHistoryObj.userId.isNotEmpty()) {
                val now = System.currentTimeMillis()
                // 融云的本地数据库数据也删 todo暂时不清楚是否需要删除
                RongCloudManager.cleanHistoryMessages(
                    callHistoryObj.userId,
                    now,
                    true,
                    object : ImOperationCallback() {
                        override fun success() {
                            Timber.d("Rong Cloud history messages cleaned successfully")
                        }

                        override fun error(code: Int?, errorMsg: String?) {
                            Timber.e("deleteMessage error: $code, $errorMsg")
                        }
                    })
            }
            // 本地删除（从UI列表中移除该具体的通话记录）
            currentList.filter { it.id != callHistoryObj.id }

        }
    }

    /**
     * 置顶/取消置顶消息
     * @param callHistoryObj 消息对象
     * @param isPinned 是否置顶
     */
    fun pinMessage(callHistoryObj: CallHistoryEntity, isPinned: Boolean) {
        updateCallHistoryPre { currentList ->
            currentList.map {
                if (it.id == callHistoryObj.id) {
                    // 修改数据库
                    DatabaseFactory.getDatabase(CallmeApplication.context)
                        .updateCurrentUserCallHistory(it)
                    // 修改本地
                    it.copy(isPinned = isPinned)
                } else {
                    it
                }

            }
        }
    }


    /**
     * 计算是否需要显示底部项
     * @param itemCount 当前列表项数量
     * @param visibleItemCount 一屏可见的项数量
     */
    fun calculateBottomItemVisibility(itemCount: Int, visibleItemCount: Int) {
        viewModelScope.launch {
            // 如果数据项不足一屏，则不显示底部项
            // 如果数据项足够多（需要滚动查看），则显示底部项
            val shouldShow = itemCount > visibleItemCount

            if (shouldShow != mShouldShowBottomItem.get()) {
                mShouldShowBottomItem.set(shouldShow)

                // 从当前列表中提取实际数据项
                val currentList = _callHistoryList.value ?: emptyList()

                // 使用新的显示逻辑更新列表
                updateList(sortCallHistory(currentList))
            }
        }

    }
}