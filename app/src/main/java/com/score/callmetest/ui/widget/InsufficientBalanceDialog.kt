package com.score.callmetest.ui.widget

import android.app.Dialog
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogInsufficientBalanceBinding
import com.score.callmetest.manager.UserInfoManager

class InsufficientBalanceDialog : BottomSheetDialogFragment() {

    private var _binding: DialogInsufficientBalanceBinding? = null
    private val binding get() = _binding!!
    private var broadcastPrice: Int = 0

    companion object {
        fun newInstance(broadcastPrice: Int): InsufficientBalanceDialog {
            val dialog = InsufficientBalanceDialog()
            val args = Bundle().apply {
                putInt("broadcast_price", broadcastPrice)
            }
            dialog.arguments = args
            return dialog
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            broadcastPrice = it.getInt("broadcast_price", 0)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, 
        container: ViewGroup?, 
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogInsufficientBalanceBinding.inflate(inflater, container, false)
        
        initViews()
        
        return binding.root
    }

    private fun initViews() {
        // 设置金币余额
        val myUserInfo = UserInfoManager.myUserInfo
        val coinBalance = myUserInfo?.availableCoins ?: 0
        binding.tvCoinBalance.text = " $coinBalance"

        // 设置广播价格文本，格式为 "Her video call price: [coin图标] 50/min"
        if (broadcastPrice > 0) {
            binding.tvBroadcastPrice.text = createPriceTextWithIcon(broadcastPrice)
        }
    }


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener {
            val bottomSheet = (dialog as? BottomSheetDialog)?.findViewById<View>(
                com.google.android.material.R.id.design_bottom_sheet
            )
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
        }
        return dialog
    }

    /**
     * 创建带coin图标的价格文本
     * 格式: "Her video call price: [coin图标] 50/min"
     */
    private fun createPriceTextWithIcon(price: Int): SpannableString {
        val text = "Her video call price:   $price/min"
        val spannableString = SpannableString(text)

        // 找到插入图标的位置（冒号后面的两个空格中的第一个）
        val iconPosition = text.indexOf(":  ")
        if (iconPosition != -1) {
            val iconInsertPosition = iconPosition + 2 // 在第一个空格位置插入图标

            // 获取coin图标drawable
            val coinDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.coin)
            coinDrawable?.let { drawable ->
                // 设置图标大小，与文字大小匹配
                val textSize = binding.tvBroadcastPrice.textSize.toInt()
                drawable.setBounds(0, 0, textSize, textSize)

                // 创建ImageSpan并插入到指定位置
                val imageSpan = ImageSpan(drawable, ImageSpan.ALIGN_BASELINE)
                spannableString.setSpan(
                    imageSpan,
                    iconInsertPosition,
                    iconInsertPosition + 1,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }

        return spannableString
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
