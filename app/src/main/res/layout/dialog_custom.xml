<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="29dp"
        android:background="@drawable/bg_dialog_rounded">

        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/login_dialog_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="32dp"
            android:paddingTop="73dp"
            android:paddingBottom="25dp">


            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:text="Thank you for taking the time to review these Terms"
                android:textColor="@color/black"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:fontFamily="@font/roboto_medium_xml"
                android:gravity="center"
                android:lineHeight="17dp"
                android:text="We hope you have a great experience with our app.  By using our app, you are agreeing to our Terms  Conditions and Privacy Policy."
                android:textColor="#808080"
                android:textSize="13sp"
                android:textStyle="normal" />

            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_cancel_layout"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="24dp"
                android:background="@drawable/bg_btn_rounded_blue"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Cancel"
                    android:textColor="#000000"
                    android:textSize="15sp"
                    android:textStyle="bold" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_confirm_layout"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/bg_btn_rounded_black"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_confirm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Confirm"
                    android:textColor="#FFFFFF"
                    android:textSize="15sp"
                    android:textStyle="bold" />

            </com.score.callmetest.ui.widget.AlphaLinearLayout>

        </LinearLayout>

    </FrameLayout>

    <ImageView
        android:id="@+id/emoji"
        android:layout_width="wrap_content"
        android:layout_height="91dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/emoji_laugh" />

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/emoji_svga"
        android:layout_width="74dp"
        android:layout_height="91dp"
        android:layout_gravity="center_horizontal"
        android:visibility="gone" />
</FrameLayout>