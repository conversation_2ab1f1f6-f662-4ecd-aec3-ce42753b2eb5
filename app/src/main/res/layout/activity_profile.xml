<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical"
    android:paddingTop="44dp">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <com.score.callmetest.ui.widget.AlphaImageView
                android:id="@+id/iv_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_centerVertical="true"
                android:src="@drawable/nav_btn_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Profile"
                android:textColor="@android:color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/btnDone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="16dp"
                android:enabled="false"
                android:paddingHorizontal="12dp"
                android:paddingVertical="5dp"
                android:text="Done"
                android:textColor="#4D000000"
                android:textSize="14sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <Space
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="#A2A2A2" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/avatar_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="My Avatar" />

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="39dp"
                    android:layout_height="39dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:scaleType="centerCrop"
                    app:shapeAppearanceOverlay="@style/CircleImageView" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />

            <!-- ID Field -->
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="55dp">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="ID" />


                <TextView
                    android:id="@+id/tvId"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@+id/btnCopy"
                    android:gravity="end"
                    android:text="12324374363" />

                <com.score.callmetest.ui.widget.AlphaTextView
                    android:id="@+id/btnCopy"
                    style="@style/SettingItemButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginHorizontal="8dp"
                    android:background="@drawable/bg_btn_copy"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="5dp"
                    android:text="Copy" />
            </RelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />
            <!-- Nickname Field -->
            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/nickname_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="Nickname" />

                <EditText
                    android:id="@+id/et_nickname"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="23dp"
                    android:background="@null"
                    android:gravity="end"
                    android:selectAllOnFocus="true"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLength="20"
                    android:minWidth="20dp"
                    android:singleLine="true"
                    android:text="sdfhuyi" />

                <ImageView
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:scaleType="center"
                    android:src="@drawable/ic_chevron_right"
                    android:tintMode="src_in"
                    app:tint="#AEAEAE" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />
            <!-- Gender Field -->
            <RelativeLayout
                android:id="@+id/gender_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="Gender" />

                <TextView
                    android:id="@+id/tv_gender"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="8dp"
                    android:gravity="end"
                    android:text="Male" />

            </RelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />
            <!-- Age Field -->
            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/age_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:visibility="visible">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="Age" />

                <TextView
                    android:id="@+id/tv_age"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="23dp"
                    android:gravity="end"
                    android:text="23" />

                <ImageView
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:scaleType="center"
                    android:src="@drawable/ic_chevron_right"
                    android:tintMode="src_in"
                    app:tint="#AEAEAE" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2"
                android:visibility="gone" />
            <!-- Regions Field -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/region_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:visibility="visible">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Country/Region"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!--  <ImageView
                      android:id="@+id/image_region"
                      android:layout_width="wrap_content"
                      android:layout_height="wrap_content"
                      android:layout_marginEnd="5dp"
                      android:src="@drawable/in"
                      app:layout_constraintBottom_toBottomOf="parent"
                      app:layout_constraintEnd_toStartOf="@id/tv_region"
                      app:layout_constraintTop_toTopOf="parent" />-->

                <TextView
                    android:id="@+id/tv_region"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="India"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/image_select"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/image_select"
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="center"
                    android:src="@drawable/ic_chevron_right"
                    android:tintMode="src_in"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="#AEAEAE" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />

            <!-- Self-introduction Field -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_introduce"
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="Self-introduction"
                    android:textColor="@android:color/black" />

                <!-- 直接使用EditText，无需NestedScrollView -->
                <EditText
                    android:id="@+id/etSelfIntroduction"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_below="@+id/tv_introduce"
                    android:layout_marginTop="20dp"
                    android:background="#F3F5FA"
                    android:gravity="top|start"
                    android:hint="Enter your signature"
                    android:maxLength="200"
                    android:maxLines="10"
                    android:minLines="3"
                    android:overScrollMode="always"
                    android:padding="8dp"
                    android:scrollbars="vertical"
                    android:textColor="@color/black"
                    android:textColorHint="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvCharCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignEnd="@+id/etSelfIntroduction"
                    android:layout_alignBottom="@+id/etSelfIntroduction"
                    android:layout_gravity="end"
                    android:layout_marginEnd="14dp"
                    android:layout_marginBottom="14dp"
                    android:text="0/200"
                    android:textColor="#BBBBBB"
                    android:textSize="10sp" />
            </RelativeLayout>

            <!-- Introduction Photos Section -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp">

                <TextView
                    android:id="@+id/tv_photos_title"
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Gallery"
                    android:textColor="@android:color/black"
                    android:textSize="16sp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/photo_recyclerview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_photos_title"
                    android:layout_marginTop="13dp" />
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>

</LinearLayout>