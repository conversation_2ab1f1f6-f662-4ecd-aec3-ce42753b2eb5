<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 主播头像 -->
        <ImageView
            android:id="@+id/avatar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="3:4"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/bg_mask"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            app:layout_constraintBottom_toBottomOf="parent" />

        <LinearLayout
            android:id="@+id/status_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="7dp"
            android:paddingVertical="3dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/status_indicator"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_marginEnd="2dp" />

            <TextView
                android:id="@+id/status_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="9sp"
                android:textStyle="bold"
                android:typeface="sans" />
        </LinearLayout>


        <!-- 昵称和语言 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="11dp"
            android:layout_marginBottom="18dp"
            android:gravity="center_vertical"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/nickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:layout_marginEnd="56dp"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:focusable="true"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:lineHeight="17dp"
                android:textStyle="bold"
                tools:text="Nickname" />

        </LinearLayout>

        <com.score.callmetest.ui.widget.AlphaSVGAImageView
            android:id="@+id/btn_video_svga"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageView
            android:id="@+id/btn_video_gray"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_gravity="center"
            android:padding="8dp"
            android:src="@drawable/video_offline"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>