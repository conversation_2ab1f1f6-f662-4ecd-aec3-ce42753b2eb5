<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="88dp"
        android:visibility="gone"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/line_tag1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="44dp"
            android:baselineAligned="false"
            android:orientation="horizontal">
            <!-- 一级Tab -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout1"
                style="@style/CustomTabStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:background="@color/transparent"
                app:tabGravity="fill"
                app:tabIndicatorHeight="0dp"
                app:tabRippleColor="@color/transparent"
                app:tabMode="scrollable" />

            <FrameLayout
                android:id="@+id/country_filter"
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:layout_gravity="center"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="22dp">

                <ImageView
                    android:id="@+id/image_home_country"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center"
                    android:src="@drawable/map_language" />

                <TextView
                    android:id="@+id/tv_home_country"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|center_horizontal"
                    android:gravity="center"
                    android:paddingStart="7dp"
                    android:paddingTop="1dp"
                    android:paddingEnd="7dp"
                    android:paddingBottom="1dp"
                    android:text="All"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </FrameLayout>

        </LinearLayout>

        <!-- 二级Tab -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout2"
            style="@style/CustomTabStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-12dp"
            android:background="@color/transparent"
            app:tabRippleColor="@color/transparent"
            app:tabGravity="fill"
            app:tabIndicatorHeight="0dp"
            app:tabMode="scrollable" />

        <!-- ViewPager2用于承载内容 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="11dp"
            android:layout_marginTop="-8dp"
            android:layout_marginEnd="11dp"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- 悬浮按钮1：orange_gift.svga -->
    <FrameLayout
        android:id="@+id/fab1_layout"
        android:layout_width="54dp"
        android:layout_height="58dp"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="105dp"
        android:visibility="gone">

        <!-- 悬浮按钮 -->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/fab1"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_gravity="top|center_horizontal"
            android:clickable="true"
            android:focusable="true"
            android:scaleType="center" />

        <!-- 计时器，只用TextView -->
        <TextView
            android:id="@+id/fab1_timer_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:textAlignment="center"
            android:paddingHorizontal="2dp"
            android:paddingVertical="2dp"
            android:text="00:00:00"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            android:textStyle="bold"
            android:background="@android:color/transparent" />
    </FrameLayout>

    <!-- 悬浮按钮2：purple_gift.svga -->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/fab2"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_gravity="bottom|end"
            android:clickable="true"
            android:focusable="true"
            android:scaleType="center"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="26dp"
            android:visibility="visible"/>
</FrameLayout>